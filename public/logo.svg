<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 784 717.72">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
        filter: url(#drop-shadow-1);
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#linear-gradient);
        stroke-width: .46px;
      }

      .cls-2, .cls-3 {
        stroke: #fff;
        stroke-miterlimit: 10;
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
        stroke-width: 1.22px;
      }
    </style>
    <linearGradient id="linear-gradient" x1="26.57" y1="237.02" x2="302.45" y2="237.02" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#6e73f7"/>
      <stop offset="1" stop-color="#927df0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="64.94" y1="539.24" x2="719.47" y2="539.24" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9ca0f2"/>
      <stop offset="1" stop-color="#d6d6f0"/>
    </linearGradient>
    <filter id="drop-shadow-1" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur" stdDeviation="21.32"/>
      <feFlood flood-color="#000" flood-opacity=".34"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-3" x1="26.56" y1="577.87" x2="731.3" y2="577.87" xlink:href="#linear-gradient-2"/>
  </defs>
  <path class="cls-2" d="M214.67,346.71v23.88c0,37.19,13.77,72.65,37.74,100.71-23.85-19.44-54.28-31.1-87.43-31.1s-66.17,12.67-90.41,33.6c25.5-28.21,39.75-64.95,39.75-103.21v-25.16c0-41.28-17.42-80.21-46.79-109.21-25.31-24.98-40.97-59.68-40.97-98.06C26.57,61.79,88.59-.04,165.02.23c73.75.24,135.04,59.74,137.36,133.46,1.28,40.42-14.84,77.04-41.4,103.02-29.61,28.97-46.3,68.6-46.3,110Z"/>
  <path class="cls-1" d="M619.56,440.27c.27.05.54.1.81.15,8.56,1.61,16.82,4.42,24.61,8.26,31.8,15.57,55.59,47.86,58.49,83.35,3.59,44.14-26.2,89.04-68.51,102.14-6.06,1.89-12,2.83-17.76,3.02-34,1.19-63.12-23.67-94.16-37.41-45.94-20.35-94.95-33.78-144.88-39.57-62.72-7.22-126.27-2.53-189.23-7.25-20.84-1.55-41.58-4.14-62.33-6.73-22.18-2.77-64.22,3.99-61.53-25.01,2.35-25.13,20.2-47.83,41.28-60.59,14.26-8.65,36.34-17.49,56.93-20.38.58-.03,1.13-.03,1.71-.03,14.59,0,28.63,2.25,41.83,6.43,15.54,6.88,30.65,16.6,46.06,25.19,2.77,3.26,5.67,6.37,8.74,9.41l.79.79c3.2,3.23,6.52,6.28,9.93,9.2,28.09,24,63.55,37.77,100.71,37.77h9.17c40.12,0,78.41-15.84,107.17-43.56,30.61-16.24,58.64-38.66,92.55-45.05,5.3-1.01,10.66-1.55,16.05-1.68,31-.7,50.55,16.87,73.51,35.98,14.64,12.18,29.69,24.88,38.1,41.97,6.56,13.33,8.57,28.42,9.49,43.25,1.06,17.25.49,35.58-8.58,50.29-9.35,15.17-26.55,24.12-44.04,27.51-17.49,3.39-35.51,1.9-53.27.41-19.41-1.64-38.83-3.28-58.24-4.92"/>
  <path class="cls-3" d="M593.35,714.49c-37.74,0-71.92-15.14-96.81-39.69-28.97-28.57-67.44-45.48-108.11-45.48h-15.38c-38.32,0-75.15,14.32-103.36,39.94-.03.03-.03.06-.06.06-2.25,2.01-4.42,4.14-6.55,6.31-22.66,23.18-53.43,38.38-87.73,41.06-3.47.27-6.91.43-10.36.43-4.17,0-8.29-.18-12.37-.55-.64-.03-1.28-.12-1.92-.18-1.55-.15-3.11-.34-4.63-.58-58.91-8.13-106.74-54.41-117.31-112.56-.73-3.9-1.25-7.89-1.61-11.91-.06-.67-.12-1.34-.18-2.01-.27-3.53-.4-7.07-.4-10.66s.15-6.88.43-10.33c2.65-34.3,17.88-65.07,41.06-87.73,2.25-2.19,4.42-4.48,6.52-6.79,23.85-20.59,54.8-33.17,88.71-33.57.58-.03,1.13-.03,1.71-.03,14.59,0,28.63,2.25,41.83,6.43,16.82,5.33,32.23,13.8,45.6,24.67.15.18.3.34.46.52,2.77,3.26,5.67,6.37,8.74,9.41l.79.79c3.2,3.23,6.52,6.28,9.93,9.2,28.09,24,63.55,37.77,100.71,37.77h9.17c40.12,0,78.41-15.84,107.17-43.56,1.52-1.43,2.99-2.89,4.45-4.42,26.08-27.17,63.21-43.68,104.15-42.31,1.28,0,2.53.06,3.81.15,15.17.91,29.73,4.33,43.2,9.81,48.89,19.86,84.05,67.2,86.21,122.31,3.08,78.72-59.83,143.51-137.84,143.51Z"/>
</svg>