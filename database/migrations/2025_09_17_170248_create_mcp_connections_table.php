<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mcp_connections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mcp_server_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->uuid('connection_id')->unique();
            $table->string('client_name')->nullable();
            $table->string('client_version')->nullable();
            $table->string('protocol_version')->default('2024-11-05');
            $table->json('client_capabilities')->nullable();
            $table->string('connection_type')->default('websocket');
            $table->string('remote_address')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_authenticated')->default(false);
            $table->timestamp('connected_at');
            $table->timestamp('last_activity');
            $table->timestamp('disconnected_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['mcp_server_id', 'is_active']);
            $table->index(['user_id', 'is_active']);
            $table->index(['last_activity']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mcp_connections');
    }
};
