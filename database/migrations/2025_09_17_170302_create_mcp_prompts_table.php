<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mcp_prompts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mcp_server_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->longText('template');
            $table->json('arguments')->nullable();
            $table->json('configuration')->nullable();
            $table->boolean('is_enabled')->default(true);
            $table->boolean('requires_auth')->default(true);
            $table->json('permissions')->nullable();
            $table->string('category')->nullable();
            $table->integer('usage_count')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['mcp_server_id', 'name']);
            $table->index(['category']);
            $table->index(['is_enabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mcp_prompts');
    }
};
