<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mcp_tools', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mcp_server_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('input_schema')->nullable();
            $table->string('handler_class');
            $table->string('handler_method')->default('handle');
            $table->json('configuration')->nullable();
            $table->boolean('is_enabled')->default(true);
            $table->boolean('requires_auth')->default(true);
            $table->json('permissions')->nullable();
            $table->integer('timeout_seconds')->default(30);
            $table->integer('max_executions_per_minute')->default(60);
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['mcp_server_id', 'name']);
            $table->index(['is_enabled']);
            $table->index(['handler_class']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mcp_tools');
    }
};
