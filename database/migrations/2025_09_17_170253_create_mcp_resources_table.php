<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mcp_resources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mcp_server_id')->constrained()->onDelete('cascade');
            $table->string('uri');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('mime_type')->default('application/json');
            $table->string('resource_type');
            $table->string('handler_class');
            $table->string('handler_method')->default('handle');
            $table->json('configuration')->nullable();
            $table->boolean('is_enabled')->default(true);
            $table->boolean('requires_auth')->default(true);
            $table->json('permissions')->nullable();
            $table->boolean('is_cacheable')->default(true);
            $table->integer('cache_ttl_seconds')->default(3600);
            $table->integer('max_size_bytes')->default(1048576);
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['mcp_server_id', 'uri']);
            $table->index(['resource_type']);
            $table->index(['is_enabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mcp_resources');
    }
};
