<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mcp_servers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('host')->default('127.0.0.1');
            $table->integer('port')->default(8080);
            $table->string('protocol_version')->default('2024-11-05');
            $table->json('capabilities')->nullable();
            $table->json('configuration')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('api_key', 64)->unique();
            $table->integer('max_connections')->default(100);
            $table->integer('current_connections')->default(0);
            $table->timestamp('last_heartbeat')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['is_active', 'last_heartbeat']);
            $table->index(['host', 'port']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mcp_servers');
    }
};
