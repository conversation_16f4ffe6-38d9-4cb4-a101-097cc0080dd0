<?php

return [
    /*
    |--------------------------------------------------------------------------
    | MCP Server Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Model Context Protocol
    | (MCP) server implementation. MCP enables AI assistants to securely
    | access external resources and tools through a standardized protocol.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Server Settings
    |--------------------------------------------------------------------------
    |
    | Basic server configuration including host, port, and connection limits.
    |
    */
    'server' => [
        'host' => env('MCP_SERVER_HOST', '127.0.0.1'),
        'port' => env('MCP_SERVER_PORT', 8080),
        'max_connections' => env('MCP_MAX_CONNECTIONS', 100),
        'timeout' => env('MCP_TIMEOUT', 30),
        'heartbeat_interval' => env('MCP_HEARTBEAT_INTERVAL', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Protocol Settings
    |--------------------------------------------------------------------------
    |
    | MCP protocol version and capabilities configuration.
    |
    */
    'protocol' => [
        'version' => '2024-11-05',
        'capabilities' => [
            'tools' => [
                'listChanged' => true,
            ],
            'resources' => [
                'subscribe' => true,
                'listChanged' => true,
            ],
            'prompts' => [
                'listChanged' => true,
            ],
            'logging' => [
                'level' => env('MCP_LOG_LEVEL', 'info'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Authentication and authorization configuration for MCP connections.
    |
    */
    'security' => [
        'require_auth' => env('MCP_REQUIRE_AUTH', true),
        'api_key_header' => 'X-MCP-API-Key',
        'allowed_origins' => explode(',', env('MCP_ALLOWED_ORIGINS', '*')),
        'rate_limit' => [
            'enabled' => env('MCP_RATE_LIMIT_ENABLED', true),
            'max_requests' => env('MCP_RATE_LIMIT_MAX_REQUESTS', 100),
            'window_minutes' => env('MCP_RATE_LIMIT_WINDOW', 1),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tool Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for MCP tools that can be executed by clients.
    |
    */
    'tools' => [
        'enabled' => env('MCP_TOOLS_ENABLED', true),
        'timeout' => env('MCP_TOOL_TIMEOUT', 30),
        'max_output_size' => env('MCP_TOOL_MAX_OUTPUT_SIZE', 1048576), // 1MB
        'allowed_tools' => [
            'link_management',
            'domain_management',
            'analytics',
            'user_management',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Resource Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for MCP resources that can be accessed by clients.
    |
    */
    'resources' => [
        'enabled' => env('MCP_RESOURCES_ENABLED', true),
        'max_size' => env('MCP_RESOURCE_MAX_SIZE', 10485760), // 10MB
        'cache_ttl' => env('MCP_RESOURCE_CACHE_TTL', 3600), // 1 hour
        'allowed_types' => [
            'links',
            'domains',
            'analytics',
            'users',
            'tags',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Prompt Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for MCP prompts that can be used by clients.
    |
    */
    'prompts' => [
        'enabled' => env('MCP_PROMPTS_ENABLED', true),
        'cache_ttl' => env('MCP_PROMPT_CACHE_TTL', 1800), // 30 minutes
        'max_length' => env('MCP_PROMPT_MAX_LENGTH', 10000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | MCP server logging settings.
    |
    */
    'logging' => [
        'enabled' => env('MCP_LOGGING_ENABLED', true),
        'channel' => env('MCP_LOG_CHANNEL', 'mcp'),
        'level' => env('MCP_LOG_LEVEL', 'info'),
        'log_requests' => env('MCP_LOG_REQUESTS', true),
        'log_responses' => env('MCP_LOG_RESPONSES', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | WebSocket Configuration
    |--------------------------------------------------------------------------
    |
    | WebSocket-specific settings for real-time communication.
    |
    */
    'websocket' => [
        'enabled' => env('MCP_WEBSOCKET_ENABLED', true),
        'ping_interval' => env('MCP_WEBSOCKET_PING_INTERVAL', 30),
        'pong_timeout' => env('MCP_WEBSOCKET_PONG_TIMEOUT', 10),
        'max_frame_size' => env('MCP_WEBSOCKET_MAX_FRAME_SIZE', 65536), // 64KB
        'compression' => env('MCP_WEBSOCKET_COMPRESSION', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Database settings for MCP server data persistence.
    |
    */
    'database' => [
        'connection' => env('MCP_DB_CONNECTION', 'default'),
        'table_prefix' => env('MCP_TABLE_PREFIX', 'mcp_'),
        'cleanup_interval' => env('MCP_CLEANUP_INTERVAL', 3600), // 1 hour
        'keep_logs_days' => env('MCP_KEEP_LOGS_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Performance and optimization settings.
    |
    */
    'performance' => [
        'enable_caching' => env('MCP_ENABLE_CACHING', true),
        'cache_driver' => env('MCP_CACHE_DRIVER', 'redis'),
        'queue_driver' => env('MCP_QUEUE_DRIVER', 'redis'),
        'async_processing' => env('MCP_ASYNC_PROCESSING', true),
        'worker_processes' => env('MCP_WORKER_PROCESSES', 4),
    ],
];
