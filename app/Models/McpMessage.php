<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * MCP Message Model
 *
 * Represents a message exchanged between an AI assistant and the MCP server.
 * Used for logging and debugging communication.
 *
 * @property int $id
 * @property int $mcp_connection_id
 * @property string $message_id
 * @property string $direction
 * @property string $method
 * @property array $params
 * @property array $result
 * @property array $error
 * @property int $response_time_ms
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class McpMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'mcp_connection_id',
        'message_id',
        'direction',
        'method',
        'params',
        'result',
        'error',
        'response_time_ms',
    ];

    protected $casts = [
        'params' => 'array',
        'result' => 'array',
        'error' => 'array',
        'response_time_ms' => 'integer',
    ];

    /**
     * Get the connection this message belongs to.
     */
    public function connection(): BelongsTo
    {
        return $this->belongsTo(McpConnection::class, 'mcp_connection_id');
    }

    /**
     * Check if this is an incoming message.
     */
    public function isIncoming(): bool
    {
        return $this->direction === 'incoming';
    }

    /**
     * Check if this is an outgoing message.
     */
    public function isOutgoing(): bool
    {
        return $this->direction === 'outgoing';
    }

    /**
     * Check if the message has an error.
     */
    public function hasError(): bool
    {
        return !empty($this->error);
    }

    /**
     * Get the message status.
     */
    public function getStatusAttribute(): string
    {
        if ($this->hasError()) {
            return 'error';
        }

        if (!empty($this->result)) {
            return 'success';
        }

        return 'pending';
    }

    /**
     * Scope to get incoming messages.
     */
    public function scopeIncoming($query)
    {
        return $query->where('direction', 'incoming');
    }

    /**
     * Scope to get outgoing messages.
     */
    public function scopeOutgoing($query)
    {
        return $query->where('direction', 'outgoing');
    }

    /**
     * Scope to get messages by method.
     */
    public function scopeByMethod($query, string $method)
    {
        return $query->where('method', $method);
    }

    /**
     * Scope to get messages with errors.
     */
    public function scopeWithErrors($query)
    {
        return $query->whereNotNull('error');
    }
}
