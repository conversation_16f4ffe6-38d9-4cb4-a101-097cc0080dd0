<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * MCP Prompt Model
 *
 * Represents a prompt template that can be used by AI assistants through the MCP server.
 * Prompts provide reusable templates for common tasks and interactions.
 *
 * @property int $id
 * @property int $mcp_server_id
 * @property string $name
 * @property string $description
 * @property string $template
 * @property array $arguments
 * @property array $configuration
 * @property bool $is_enabled
 * @property bool $requires_auth
 * @property array $permissions
 * @property string $category
 * @property int $usage_count
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 */
class McpPrompt extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'mcp_server_id',
        'name',
        'description',
        'template',
        'arguments',
        'configuration',
        'is_enabled',
        'requires_auth',
        'permissions',
        'category',
        'usage_count',
    ];

    protected $casts = [
        'arguments' => 'array',
        'configuration' => 'array',
        'is_enabled' => 'boolean',
        'requires_auth' => 'boolean',
        'permissions' => 'array',
        'usage_count' => 'integer',
    ];

    protected $attributes = [
        'is_enabled' => true,
        'requires_auth' => true,
        'usage_count' => 0,
        'arguments' => '[]',
        'configuration' => '{}',
        'permissions' => '[]',
    ];

    /**
     * Get the MCP server this prompt belongs to.
     */
    public function server(): BelongsTo
    {
        return $this->belongsTo(McpServer::class, 'mcp_server_id');
    }

    /**
     * Get the usage logs for this prompt.
     */
    public function usages(): HasMany
    {
        return $this->hasMany(McpPromptUsage::class);
    }

    /**
     * Check if the prompt can be used by the given user.
     */
    public function canBeUsedBy(?User $user = null): bool
    {
        if (!$this->is_enabled) {
            return false;
        }

        if ($this->requires_auth && !$user) {
            return false;
        }

        if ($user && !empty($this->permissions)) {
            return $user->hasAnyPermission($this->permissions);
        }

        return true;
    }

    /**
     * Render the prompt template with given arguments.
     */
    public function render(array $arguments = []): string
    {
        $template = $this->template;
        
        foreach ($arguments as $key => $value) {
            $template = str_replace("{{$key}}", $value, $template);
        }

        return $template;
    }

    /**
     * Validate arguments against the prompt's schema.
     */
    public function validateArguments(array $arguments): array
    {
        $errors = [];
        $requiredArgs = $this->arguments ?? [];

        foreach ($requiredArgs as $arg) {
            $name = $arg['name'] ?? '';
            $required = $arg['required'] ?? false;
            $type = $arg['type'] ?? 'string';

            if ($required && !array_key_exists($name, $arguments)) {
                $errors[] = "Required argument '{$name}' is missing";
                continue;
            }

            if (array_key_exists($name, $arguments)) {
                $value = $arguments[$name];
                if (!$this->validateArgumentType($value, $type)) {
                    $errors[] = "Argument '{$name}' must be of type {$type}";
                }
            }
        }

        return $errors;
    }

    /**
     * Validate an argument type.
     */
    private function validateArgumentType($value, string $type): bool
    {
        return match ($type) {
            'string' => is_string($value),
            'integer' => is_int($value),
            'number' => is_numeric($value),
            'boolean' => is_bool($value),
            'array' => is_array($value),
            default => true,
        };
    }

    /**
     * Increment the usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * Get the prompt's configuration value.
     */
    public function getConfiguration(string $key, $default = null)
    {
        return data_get($this->configuration, $key, $default);
    }

    /**
     * Scope to get only enabled prompts.
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Scope to get prompts by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }
}
